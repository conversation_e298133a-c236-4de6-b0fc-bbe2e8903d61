#!/usr/bin/env python3
"""
ScrapeNinja Asynchronous Web Scraper
====================================

This script processes JSON files containing business data, extracts website URLs,
crawls those websites using ScrapeNinja API, and parses the content using Gemini AI.

Usage:
    python run_scraper.py [options]

Options:
    --input-dir DIR         Directory containing input JSON files (default: input)
    --output-dir DIR        Directory for output files (default: output)
    --scraped-data-dir DIR  Directory for scraped data (default: scraped_data)
    --max-pages N           Maximum pages to scrape per website (default: 20)
    --max-depth N           Maximum crawl depth (default: 2)
    --delay SECONDS         Delay between requests (default: 0)
    --single-file FILE      Process only a specific input file
    --single-url URL        Scrape and parse a single URL (for testing)
    --help                  Show this help message

Examples:
    # Process all files in input directory
    python run_scraper.py

    # Process with custom settings
    python run_scraper.py --max-pages 10 --delay 2.0

    # Process only one file
    python run_scraper.py --single-file "input/Wichita KS.json"

    # Test with a single URL
    python run_scraper.py --single-url "https://example-vet.com"
"""

import asyncio
import argparse
import sys
import os
import json
import time
import threading
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional

from async_scraper import AsyncWebCrawler, CrawlConfig
from scraper_orchestrator import ScraperOrchestrator
from gemini_parser import GeminiParser

class ProgressTracker:
    """Real-time progress tracking with footer progress bar"""

    def __init__(self):
        self.total_businesses = 0
        self.completed_businesses = 0
        self.current_phase = "Initializing"
        self.start_time = time.time()
        self.crawl_stats = {"success": 0, "failed": 0}
        self.parse_stats = {"success": 0, "failed": 0}
        self.running = False
        self.progress_thread = None

    def start(self, total_businesses: int):
        """Start the progress tracker"""
        self.total_businesses = total_businesses
        self.completed_businesses = 0
        self.start_time = time.time()
        self.running = True

        # Start progress bar in separate thread
        self.progress_thread = threading.Thread(target=self._show_progress_bar, daemon=True)
        self.progress_thread.start()

    def stop(self):
        """Stop the progress tracker"""
        self.running = False
        if self.progress_thread:
            self.progress_thread.join(timeout=1)
        # Clear the progress bar line
        print("\r" + " " * 100 + "\r", end="", flush=True)

    def update_phase(self, phase: str):
        """Update current processing phase"""
        self.current_phase = phase

    def update_crawl_stats(self, success: bool):
        """Update crawling statistics"""
        if success:
            self.crawl_stats["success"] += 1
        else:
            self.crawl_stats["failed"] += 1

    def update_parse_stats(self, success: bool):
        """Update parsing statistics"""
        if success:
            self.parse_stats["success"] += 1
        else:
            self.parse_stats["failed"] += 1
        self.completed_businesses += 1

    def _show_progress_bar(self):
        """Show real-time progress bar in footer"""
        while self.running:
            if self.total_businesses > 0:
                progress = self.completed_businesses / self.total_businesses
                bar_length = 30
                filled_length = int(bar_length * progress)
                bar = "█" * filled_length + "░" * (bar_length - filled_length)

                elapsed = time.time() - self.start_time
                if progress > 0:
                    eta = (elapsed / progress) * (1 - progress)
                    eta_str = f"{int(eta//60):02d}:{int(eta%60):02d}"
                else:
                    eta_str = "--:--"

                # Create progress line
                progress_line = (
                    f"\r🚀 {self.current_phase} │ "
                    f"[{bar}] {progress*100:.1f}% │ "
                    f"{self.completed_businesses}/{self.total_businesses} │ "
                    f"✅{self.crawl_stats['success']} ❌{self.crawl_stats['failed']} │ "
                    f"ETA: {eta_str}"
                )

                print(progress_line, end="", flush=True)

            time.sleep(0.5)

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="ScrapeNinja Asynchronous Web Scraper",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )

    parser.add_argument(
        '--input-dir',
        default='input',
        help='Directory containing input JSON files (default: input)'
    )

    parser.add_argument(
        '--output-dir',
        default='output',
        help='Directory for output files (default: output)'
    )

    parser.add_argument(
        '--scraped-data-dir',
        default='scraped_data',
        help='Directory for scraped data (default: scraped_data)'
    )

    parser.add_argument(
        '--max-pages',
        type=int,
        default=20,
        help='Maximum pages to scrape per website (default: 20)'
    )

    parser.add_argument(
        '--max-depth',
        type=int,
        default=2,
        help='Maximum crawl depth (default: 2)'
    )

    parser.add_argument(
        '--delay',
        type=float,
        default=0,
        help='Delay between requests in seconds (default: 0)'
    )

    parser.add_argument(
        '--single-file',
        help='Process only a specific input file'
    )

    parser.add_argument(
        '--single-url',
        help='Scrape and parse a single URL (for testing)'
    )

    parser.add_argument(
        '--max-concurrent',
        type=int,
        default=3,
        help='Maximum concurrent businesses to process (default: 3)'
    )

    return parser.parse_args()

async def process_single_url(url: str, config: CrawlConfig):
    """Process a single URL for testing purposes"""
    print(f"🧪 Single URL Test Mode")
    print(f"=" * 60)
    print(f"🎯 Target URL: {url}")
    print()

    start_time = time.time()

    try:
        # Step 1: Scrape the website
        print(f"🕷️  Step 1: Crawling website...")
        crawler = AsyncWebCrawler(config)
        crawl_start = time.time()

        crawl_results = await crawler.crawl_website(url, "test_scraped_data")

        crawl_time = time.time() - crawl_start
        pages_count = len(crawl_results['scraped_content'])

        print(f"✅ Crawling completed in {crawl_time:.1f}s")
        print(f"   📄 Pages scraped: {pages_count}")
        print(f"   📁 Data saved to: test_scraped_data/")
        print()

        # Step 2: Combine content
        print(f"📝 Step 2: Combining scraped content...")
        combine_start = time.time()

        combined_content = []
        combined_content.append(f"# Test Crawl Results")
        combined_content.append(f"# URL: {url}")
        combined_content.append(f"# Scraped on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        combined_content.append("")

        for i, page_data in enumerate(crawl_results['scraped_content'], 1):
            page_url = page_data.get('url', 'Unknown URL')
            content = page_data.get('content', '')

            combined_content.append("=" * 80)
            combined_content.append(f"Page {i}: {page_url}")
            combined_content.append("=" * 80)
            combined_content.append("")
            combined_content.append(content)
            combined_content.append("")

        combined_text = "\n".join(combined_content)
        combine_time = time.time() - combine_start

        # Save combined content
        os.makedirs("test_output", exist_ok=True)
        with open("test_output/test_combined.md", 'w', encoding='utf-8') as f:
            f.write(combined_text)

        print(f"✅ Content combined in {combine_time:.1f}s")
        print(f"   📄 Combined file: test_output/test_combined.md")
        print(f"   📊 Total content length: {len(combined_text):,} characters")
        print()

        # Step 3: Parse with Gemini
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        if gemini_api_key:
            print(f"🤖 Step 3: Parsing with Gemini AI...")
            parse_start = time.time()

            parser = GeminiParser(gemini_api_key)
            parsed_data = parser.parse_content(combined_text, homepage_url=url)

            parse_time = time.time() - parse_start

            # Save parsed results
            with open("test_output/test_parsed.json", 'w', encoding='utf-8') as f:
                json.dump(parsed_data, f, indent=2, ensure_ascii=False)

            print(f"✅ Parsing completed in {parse_time:.1f}s")
            print(f"   🤖 Parsed file: test_output/test_parsed.json")

            # Show parsing results summary
            if isinstance(parsed_data, dict):
                services_count = len(parsed_data.get('services', []))
                phone_count = len(parsed_data.get('phone_numbers', []))
                print(f"   📋 Services found: {services_count}")
                print(f"   📞 Phone numbers found: {phone_count}")
            print()
        else:
            print(f"⚠️  Step 3: Skipping Gemini parsing")
            print(f"   ❌ GEMINI_API_KEY not found in environment")
            print(f"   💡 Add your Gemini API key to .env file to enable parsing")
            print()

        # Final summary
        total_time = time.time() - start_time
        print(f"🎉 Single URL test completed successfully!")
        print(f"📊 Performance Summary:")
        print(f"   🕷️  Crawling: {crawl_time:.1f}s")
        if gemini_api_key:
            print(f"   🤖 Parsing: {parse_time:.1f}s")
        print(f"   ⏱️  Total time: {total_time:.1f}s")
        print()
        print(f"📁 Generated files in test_output/:")
        print(f"   📄 test_combined.md - Combined scraped content")
        if gemini_api_key:
            print(f"   🤖 test_parsed.json - AI-parsed structured data")

    except Exception as e:
        print(f"❌ Error processing URL: {e}")
        print()
        print(f"🔧 Troubleshooting tips:")
        print(f"   1. Check your internet connection")
        print(f"   2. Verify SCRAPENINJA_API_KEY in .env file")
        print(f"   3. Try a different URL")
        print(f"   4. Check if the website blocks automated requests")

async def main():
    """Main function"""
    print("🚀 ScrapeNinja Asynchronous Web Scraper")
    print("=" * 60)

    args = parse_arguments()
    start_time = time.time()

    # Step 1: Environment validation
    print("🔧 Step 1: Environment Validation")
    print("-" * 40)

    scrapeninja_key = os.getenv('SCRAPENINJA_API_KEY')
    gemini_key = os.getenv('GEMINI_API_KEY')

    if not scrapeninja_key:
        print("❌ SCRAPENINJA_API_KEY not found in environment variables")
        print("💡 Please add it to your .env file")
        print()
        print("🔧 Troubleshooting:")
        print("   1. Create a .env file in the project root")
        print("   2. Add: SCRAPENINJA_API_KEY=your_api_key_here")
        print("   3. Get your API key from https://scrapeninja.net")
        sys.exit(1)
    else:
        print("✅ SCRAPENINJA_API_KEY found")

    if not gemini_key:
        print("⚠️  GEMINI_API_KEY not found in environment variables")
        print("   🕷️  Scraping will work but AI parsing will be skipped")
        print("   💡 Add your Gemini API key to .env file to enable parsing")
    else:
        print("✅ GEMINI_API_KEY found")
    print()

    # Step 2: Configuration setup
    print("⚙️  Step 2: Configuration Setup")
    print("-" * 40)

    config = CrawlConfig(
        max_pages=args.max_pages,
        max_depth=args.max_depth,
        delay_between_requests=args.delay,
        timeout=30,
        retries=3
    )

    print(f"🕷️  Crawl Configuration:")
    print(f"   📄 Max pages per site: {config.max_pages}")
    print(f"   🔍 Max depth: {config.max_depth}")
    print(f"   ⏱️  Delay between requests: {config.delay_between_requests}s")
    print(f"   🔄 Max concurrent businesses: {args.max_concurrent}")
    print(f"   ⏰ Request timeout: {config.timeout}s")
    print(f"   🔁 Retry attempts: {config.retries}")
    print()

    # Handle single URL testing
    if args.single_url:
        await process_single_url(args.single_url, config)
        return

    # Step 3: Initialize orchestrator
    print("🎯 Step 3: Initializing Scraper Orchestrator")
    print("-" * 40)

    orchestrator = ScraperOrchestrator(
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        scraped_data_dir=args.scraped_data_dir,
        crawl_config=config
    )

    print(f"📁 Input directory: {args.input_dir}")
    print(f"📁 Output directory: {args.output_dir}")
    print(f"📁 Scraped data directory: {args.scraped_data_dir}")
    print()

    # Step 4: Process files
    print("🚀 Step 4: Processing Business Data")
    print("-" * 40)

    if args.single_file:
        # Single file processing
        if not os.path.exists(args.single_file):
            print(f"❌ Error: File {args.single_file} not found")
            print()
            print("🔧 Troubleshooting:")
            print(f"   1. Check if the file path is correct")
            print(f"   2. Ensure the file exists in the specified location")
            print(f"   3. Try using an absolute path")
            sys.exit(1)

        print(f"📄 Processing single file: {Path(args.single_file).name}")
        print(f"🔄 Max concurrent businesses: {args.max_concurrent}")
        print()

        # Count businesses in file for progress tracking
        try:
            with open(args.single_file, 'r', encoding='utf-8') as f:
                businesses_data = json.load(f)
                total_businesses = len(businesses_data)
        except Exception as e:
            print(f"❌ Error reading file: {e}")
            sys.exit(1)

        # Initialize progress tracker
        progress_tracker = ProgressTracker()
        progress_tracker.start(total_businesses)
        progress_tracker.update_phase("Processing businesses")

        try:
            processing_start = time.time()
            results = await orchestrator.process_input_file(args.single_file, args.max_concurrent)
            processing_time = time.time() - processing_start

            progress_tracker.stop()

            print(f"✅ Single file processing completed!")
            print(f"📊 Results Summary:")
            print(f"   📄 File: {Path(args.single_file).name}")
            print(f"   🏢 Businesses processed: {len(results)}")
            print(f"   ⏱️  Processing time: {processing_time:.1f}s")
            print(f"   🚀 Average time per business: {processing_time/len(results):.1f}s" if results else "   🚀 Average time per business: N/A")

        except Exception as e:
            progress_tracker.stop()
            print(f"❌ Error processing file: {e}")
            sys.exit(1)

    else:
        # Multiple files processing
        print(f"📁 Processing all files in: {args.input_dir}")
        print(f"🔄 Max concurrent businesses per file: {args.max_concurrent}")
        print()

        input_files = orchestrator.find_input_files()
        if not input_files:
            print(f"❌ No JSON files found in {args.input_dir}")
            print()
            print("🔧 Troubleshooting:")
            print(f"   1. Check if the directory exists: {args.input_dir}")
            print(f"   2. Ensure there are .json files in the directory")
            print(f"   3. Verify file permissions")
            return

        print(f"📋 Found {len(input_files)} input files:")
        total_businesses_all = 0

        # Count total businesses across all files
        for input_file in input_files:
            try:
                with open(input_file, 'r', encoding='utf-8') as f:
                    businesses_data = json.load(f)
                    business_count = len(businesses_data)
                    total_businesses_all += business_count
                    print(f"   📄 {Path(input_file).name}: {business_count} businesses")
            except Exception as e:
                print(f"   ❌ {Path(input_file).name}: Error reading file - {e}")

        print(f"   🏢 Total businesses to process: {total_businesses_all}")
        print()

        # Initialize progress tracker for all files
        progress_tracker = ProgressTracker()
        progress_tracker.start(total_businesses_all)

        all_results = {}
        overall_start = time.time()

        for i, input_file in enumerate(input_files, 1):
            filename = Path(input_file).stem
            progress_tracker.update_phase(f"File {i}/{len(input_files)}: {filename}")

            try:
                file_start = time.time()
                results = await orchestrator.process_input_file(input_file, args.max_concurrent)
                file_time = time.time() - file_start

                all_results[filename] = results

                # Update progress for completed businesses
                for _ in results:
                    progress_tracker.update_parse_stats(True)

            except Exception as e:
                print(f"\n❌ Error processing {input_file}: {e}")
                all_results[filename] = []

        progress_tracker.stop()
        overall_time = time.time() - overall_start

        # Step 5: Save summary results
        print(f"\n📊 Step 5: Generating Summary Report")
        print("-" * 40)

        summary_file = os.path.join(args.output_dir, "all_results_summary.json")
        summary = {
            'timestamp': datetime.now().isoformat(),
            'total_files_processed': len(input_files),
            'total_businesses_processed': sum(len(results) for results in all_results.values()),
            'processing_time_seconds': overall_time,
            'max_concurrent': args.max_concurrent,
            'files': all_results
        }

        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        # Final summary
        total_businesses_processed = sum(len(file_results) for file_results in all_results.values())
        successful_files = len([f for f in all_results.values() if f])

        print(f"🎉 Batch processing completed successfully!")
        print(f"📊 Final Summary:")
        print(f"   📁 Files processed: {len(all_results)}/{len(input_files)}")
        print(f"   ✅ Successful files: {successful_files}")
        print(f"   🏢 Total businesses processed: {total_businesses_processed}")
        print(f"   ⏱️  Total processing time: {overall_time:.1f}s")
        if total_businesses_processed > 0:
            print(f"   🚀 Average time per business: {overall_time/total_businesses_processed:.1f}s")
        print(f"   📄 Summary report: {summary_file}")
        print()

        print(f"📋 Per-file Results:")
        for filename, file_results in all_results.items():
            status = "✅" if file_results else "❌"
            print(f"   {status} {filename}: {len(file_results)} businesses")

    # Final completion message
    total_time = time.time() - start_time
    print()
    print(f"🎉 ScrapeNinja processing completed!")
    print(f"⏱️  Total execution time: {total_time:.1f}s")
    print(f"📁 Check output directories for results:")
    print(f"   📄 Raw scraped data: {args.scraped_data_dir}/")
    print(f"   🤖 Processed results: {args.output_dir}/")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n")
        print("⏹️  Operation cancelled by user")
        print("🔧 Tip: Use Ctrl+C to stop the scraper at any time")
        sys.exit(1)
    except Exception as e:
        print("\n")
        print(f"❌ Unexpected error occurred: {e}")
        print()
        print("🔧 Troubleshooting steps:")
        print("   1. Check your internet connection")
        print("   2. Verify API keys in .env file")
        print("   3. Ensure input files are valid JSON")
        print("   4. Check file permissions")
        print("   5. Try running with --single-url for testing")
        print()
        print("💡 For support, check the documentation or contact support")
        sys.exit(1)

{"start_url": "http://www.thebirdvet.com/", "timestamp": 1748298778, "config": {"max_pages": 50, "max_depth": 2, "pages_scraped": 1}, "visited_urls": ["http://www.thebirdvet.com/"], "failed_urls": [], "scraped_content": [{"url": "http://www.thebirdvet.com/", "content": "OCTYPE html>\n\n# thebirdvet.com\n\n![Network Solutions](/__media__/pics/10667/netsol-logos-2020-165-50.jpg) \n\nthebirdvet.com expired on and is pending renewal or deletion. \n\n[ Renew Your Domain Now](//ads.networksolutions.com/landing?code=P47C100S1N0B9A1D124E0000V100) \n\n[ Backorder Domain](https://www.networksolutions.com/promotions/ref/REPOINT-DPRD.html?dom=thebirdvet.com) \n\n[Trademark Free](//thebirdvet.com/%5F%5Fmedia%5F%5F/js/trademark.php?d=thebirdvet.com&type=ns)   \n  \n[Review our Privacy Policy](https://customerservice.web.com/prweb/PRAuth/app/WebKM%5F/JfLhd8LVz0a16-h3GqsHOCqqFky5N%5Fvd%2A/!autoThread2?pzuiactionzzz=CXtycX03MzYwYWU1Njk5NTk3MWFhMzJjYTZlMjc2YzVhYjllNTE0YTJiMzcwM2NmMDc0YTQwZDdlNTYyNDhjZmJmNzA4ODA2NWIwMjgwZjA2OTFmMGNiMTk0NzFhNjE5ZWUzMTMxYzUwZDI0Y2Q2ZDdhZTlmMzVkMjIyMjEzZGQ1MGE4MzJmZDYyMzRiZWUwYWY5ZTRiNmYwMTY2YjliMWVlN2JhMjYyODc5MmUyOGUxODNlM2U0MmU4M2MyOTA0MWRiYzE%3D%2A)   \n  \n[Service Agreement](https://assets.web.com/legal/English/MSA/v1.0.0.3/ServicesAgreement.pdf)   \n  \n[Legal Notice](https://www.networksolutions.com/)   \n  ", "links": ["https://www.networksolutions.com/promotions/ref/REPOINT-DPRD.html?dom=thebirdvet.com", "https://customerservice.web.com/prweb/PRAuth/app/WebKM%5F/JfLhd8LVz0a16-h3GqsHOCqqFky5N%5Fvd%2A/!autoThread2?pzuiactionzzz=CXtycX03MzYwYWU1Njk5NTk3MWFhMzJjYTZlMjc2YzVhYjllNTE0YTJiMzcwM2NmMDc0YTQwZDdlNTYyNDhjZmJmNzA4ODA2NWIwMjgwZjA2OTFmMGNiMTk0NzFhNjE5ZWUzMTMxYzUwZDI0Y2Q2ZDdhZTlmMzVkMjIyMjEzZGQ1MGE4MzJmZDYyMzRiZWUwYWY5ZTRiNmYwMTY2YjliMWVlN2JhMjYyODc5MmUyOGUxODNlM2U0MmU4M2MyOTA0MWRiYzE%3D%2A", "https://assets.web.com/legal/English/MSA/v1.0.0.3/ServicesAgreement.pdf", "https://www.networksolutions.com/"], "status": "success", "timestamp": 1748298778.154654}]}